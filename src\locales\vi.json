{"common": {"loading": "<PERSON><PERSON> tả<PERSON>...", "error": "Đ<PERSON> xảy ra lỗi", "retry": "<PERSON><PERSON><PERSON> lại", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "ok": "OK", "delete": "Xóa", "confirmDelete": "<PERSON><PERSON><PERSON>n x<PERSON>a", "confirmDeleteItem": "Bạn có chắc chắn muốn xóa mục này?", "confirmDeleteWithName": "Bạn có chắc chắn muốn xóa \"{{itemName}}\"?", "bulkDelete": "<PERSON><PERSON><PERSON>", "edit": "Chỉnh sửa", "view": "Xem", "create": "<PERSON><PERSON><PERSON> mới", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "sort": "<PERSON><PERSON><PERSON>p", "settings": "Cài đặt", "profile": "<PERSON><PERSON> sơ", "logout": "<PERSON><PERSON><PERSON> xu<PERSON>", "language": "<PERSON><PERSON><PERSON>", "theme": "<PERSON><PERSON><PERSON>", "light": "<PERSON><PERSON><PERSON>", "dark": "<PERSON><PERSON><PERSON>", "custom": "<PERSON><PERSON><PERSON> chỉnh", "open": "Mở", "add": "<PERSON><PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "actions": "<PERSON><PERSON>", "close": "Đ<PERSON><PERSON>", "reset": "Đặt lại", "menu": "<PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "dateRange": "<PERSON><PERSON><PERSON><PERSON> thời gian", "columns": "<PERSON><PERSON><PERSON>", "selectAll": "<PERSON><PERSON><PERSON> tất cả", "startDate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "endDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "code": "<PERSON><PERSON> k<PERSON>ến mãi", "name": "<PERSON><PERSON><PERSON> hiển thị", "discountValue": "<PERSON><PERSON><PERSON> trị giảm", "minimumOrderValue": "<PERSON><PERSON><PERSON> trị đơn hàng tối thiểu", "maximumDiscountAmount": "<PERSON><PERSON><PERSON><PERSON> giá tối đa", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "addNew": "<PERSON><PERSON><PERSON><PERSON> mới", "searchPlaceholder": "<PERSON><PERSON><PERSON> k<PERSON>...", "backToHome": "Quay lại trang chủ", "level": "<PERSON><PERSON><PERSON>", "activate": "<PERSON><PERSON><PERSON>", "deactivate": "Tắt", "details": "<PERSON>em chi tiết", "home": "Trang chủ", "componentsText": "Components", "viewExample": "Xem ví dụ", "products": "<PERSON><PERSON><PERSON> p<PERSON>m", "services": "<PERSON><PERSON><PERSON> v<PERSON>", "service1": "Dịch vụ 1", "service2": "Dịch vụ 2", "service3": "Dịch vụ 3", "contact": "<PERSON><PERSON><PERSON>", "inbox": "<PERSON><PERSON><PERSON> thư đ<PERSON>n", "notifications": "<PERSON><PERSON><PERSON><PERSON> báo", "animation": "<PERSON><PERSON><PERSON>", "help": "Trợ giúp & Hỗ trợ", "noResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "forbidden": {"title": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> bị từ chối", "message": "Bạn không có quyền truy cập vào trang này. Vui lòng liên hệ quản trị viên nếu bạn cho rằng đây là lỗi."}, "goBack": "Quay lại", "back": "Quay lại", "refresh": "<PERSON><PERSON><PERSON>", "login": "<PERSON><PERSON><PERSON>", "aiAgents": {"title": "AI Agents", "description": "<PERSON><PERSON><PERSON><PERSON> lý các AI agents và trợ lý của bạn", "noAgentsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy agent nào phù hợp với tiêu chí tìm kiếm"}, "backToHomePage": "<PERSON><PERSON> trang chủ", "selected": "{{count}} đ<PERSON> ch<PERSON>n", "createOption": "T<PERSON><PERSON> \"{{option}}\"", "backToButtons": "Quay lại Buttons", "backToFormSections": "Quay lại Form Sections", "expandAll": "Mở tất cả", "collapseAll": "<PERSON><PERSON><PERSON> tất cả", "primary": "<PERSON><PERSON><PERSON>", "secondary": "<PERSON><PERSON>", "outline": "<PERSON><PERSON><PERSON><PERSON>", "ghost": "Mờ", "success": "<PERSON><PERSON><PERSON><PERSON> công", "warning": "<PERSON><PERSON><PERSON> b<PERSON>o", "danger": "<PERSON><PERSON>", "small": "Nhỏ", "medium": "Vừa", "large": "Lớn", "disabled": "<PERSON><PERSON> hi<PERSON> h<PERSON>a", "createNew": "<PERSON><PERSON><PERSON> mới", "nextStep": "<PERSON><PERSON><PERSON><PERSON> tiế<PERSON> theo", "loading_state": "<PERSON><PERSON> t<PERSON>", "action": "<PERSON><PERSON><PERSON> đ<PERSON>", "ui": {"badge": {"new": "<PERSON><PERSON><PERSON>", "count": "{{count}}"}, "card": {"title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "subtitle": "Ti<PERSON><PERSON> đề phụ", "content": "<PERSON><PERSON><PERSON> là nội dung của card.", "footer": "<PERSON><PERSON> trang", "loading": "<PERSON><PERSON> tả<PERSON>...", "withFooter": {"content": "<PERSON> này có footer v<PERSON><PERSON> các nút hành động."}, "withIcon": {"content": "Card này có icon trong header."}, "bordered": {"content": "<PERSON> này có viền."}, "customHeader": {"content": "<PERSON> này có header tùy chỉnh."}}, "badge_alt": {"new": "<PERSON><PERSON><PERSON>", "count": "{{count}}"}, "tabs": {"tab": "Tab {{number}}", "content": "Nội dung tab {{number}}"}, "accordion": {"item": "<PERSON><PERSON><PERSON> {{number}}", "content": "<PERSON><PERSON>i dung mục {{number}}", "expandAll": "Mở rộng tất cả", "collapseAll": "<PERSON><PERSON> g<PERSON>n tất cả"}}}, "auth": {"login": "<PERSON><PERSON><PERSON>", "register": "<PERSON><PERSON><PERSON> ký", "forgotPassword": "<PERSON><PERSON><PERSON><PERSON> mật k<PERSON>u", "resetPassword": "Đặt lại mật khẩu", "email": "Email", "password": "<PERSON><PERSON><PERSON>", "confirmPassword": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "fullName": "Họ và tên", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "rememberMe": "<PERSON><PERSON> nhớ đăng nhập", "signIn": "<PERSON><PERSON><PERSON>", "signUp": "<PERSON><PERSON><PERSON> ký", "orContinueWith": "Hoặc tiếp tục với", "forgotPasswordDescription": "<PERSON>hập địa chỉ email của bạn và chúng tôi sẽ gửi cho bạn liên kết để đặt lại mật khẩu.", "loginError": "<PERSON><PERSON><PERSON> nhập thất bại. <PERSON><PERSON> lòng kiểm tra thông tin đăng nhập và thử lại.", "registerError": "<PERSON><PERSON><PERSON> ký thất bại. <PERSON><PERSON> lòng thử lại sau.", "recaptchaError": "<PERSON><PERSON> lòng xác nhận bạn không phải là robot.", "verifyAccount": "<PERSON><PERSON><PERSON> thực tà<PERSON>n", "verifyEmailDescription": "<PERSON><PERSON>g tôi đã gửi mã xác thực đến email {{email}}. <PERSON><PERSON> lòng kiểm tra hộp thư đến của bạn.", "verifySmsDescription": "<PERSON><PERSON>g tôi đã gửi mã xác thực đến số điện thoại {{phone}}. <PERSON><PERSON> lòng kiểm tra tin nhắn của bạn.", "verifyDescription": "<PERSON><PERSON>g tôi đã gửi mã xác thực. <PERSON><PERSON> lòng kiểm tra email hoặc tin nhắn của bạn.", "resendCode": "<PERSON><PERSON>i lại mã x<PERSON>c thực", "codeSent": "<PERSON><PERSON> xác thực đã được gửi lại.", "resendFailed": "<PERSON><PERSON><PERSON><PERSON> thể gửi lại mã xác thực. <PERSON><PERSON> lòng thử lại.", "backToLogin": "Quay lại đăng nh<PERSON>p", "twoFactorAuth": "<PERSON><PERSON><PERSON> thực hai yếu tố", "twoFactorAuthDescription": "<PERSON><PERSON> lòng nhập mã xác thực để tiếp tục.", "verificationCode": "<PERSON><PERSON> xác thực", "verify": "<PERSON><PERSON><PERSON> th<PERSON>c", "didntReceiveCode": "Bạn chưa nhận được mã?", "passwordRequirements": "<PERSON><PERSON><PERSON> khẩu phải có ít nhất 8 ký tự, bao gồ<PERSON> chữ hoa, chữ thườ<PERSON>, số và ký tự đặc biệt."}, "chat": {"newChat": "<PERSON><PERSON><PERSON><PERSON> trò chuyện mới", "sendMessage": "<PERSON><PERSON><PERSON> tin nh<PERSON>n", "typeMessage": "<PERSON><PERSON><PERSON><PERSON> tin nhắn...", "typeSlashForMenu": "<PERSON><PERSON><PERSON><PERSON> / để chọn nhanh menu...", "uploadFile": "<PERSON><PERSON><PERSON> lên t<PERSON>", "uploadFromComputer": "<PERSON><PERSON><PERSON> lên từ máy t<PERSON>h", "uploadFromGoogleDrive": "<PERSON><PERSON><PERSON><PERSON> từ Google Drive", "webSearch": "<PERSON><PERSON><PERSON> k<PERSON> web", "voiceInput": "<PERSON><PERSON>ập bằng giọng nói", "aiAgents": "AI Agents", "aiAssistants": "<PERSON><PERSON><PERSON>", "specializedAgents": "Agents ch<PERSON><PERSON><PERSON>", "selectAgent": "Chọn Agent", "maxFilesExceeded": "Tối đa {{max}} tệp đ<PERSON><PERSON><PERSON> phép tải lên", "invalidFileTypes": "<PERSON>ột số tệp không được tải lên vì định dạng không được hỗ trợ. Định dạng cho phép: hình ảnh, PDF, XLSX, CSV, DOCX, JSON, MD, JSONL", "errorProcessingFiles": "Lỗi khi xử lý tệp. <PERSON><PERSON> lòng thử lại.", "chooseFeature": "<PERSON><PERSON><PERSON> t<PERSON>h năng bạn muốn sử dụng", "payment": "<PERSON><PERSON> <PERSON><PERSON>", "imagePasted": "Đã dán <PERSON>nh từ clipboard", "connected": "<PERSON><PERSON> kết nối", "connecting": "<PERSON><PERSON> kết n<PERSON>i", "connectionError": "Lỗi kết nối", "reconnect": "<PERSON><PERSON><PERSON><PERSON> lại", "notConnected": "<PERSON><PERSON><PERSON> k<PERSON>", "sending": "<PERSON><PERSON> g<PERSON>...", "sendMessageError": "<PERSON><PERSON><PERSON><PERSON> thể gửi tin nhắn", "aiTyping": "AI đang soạn tin...", "aiResponding": "AI đang phản hồi...", "websocketMode": "Chế độ WebSocket", "demoMode": "<PERSON><PERSON> độ Demo", "realTimeChat": "<PERSON>t thời gian thực", "simulatedChat": "Chat mô phỏng"}, "marketing": {"title": "Marketing", "description": "Quản lý chiến dịch marketing, quảng cáo và khuyến mãi"}, "data": {"title": "Dữ liệu & <PERSON>ân tích", "description": "<PERSON>em và phân tích dữ liệu, báo cáo và thống kê"}, "marketplace": {"title": "Marketplace", "description": "<PERSON><PERSON><PERSON><PERSON> phá và mua sắm sản phẩm, dị<PERSON> vụ và tài nguyên"}, "viewPanel": {"welcome": "Chào mừng đến với AI ERP", "recentChats": "<PERSON><PERSON><PERSON><PERSON> trò chuyện gần đây", "favorites": "<PERSON><PERSON><PERSON>ch", "history": "<PERSON><PERSON><PERSON> s<PERSON>", "noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "loadMore": "<PERSON><PERSON><PERSON>ê<PERSON>"}, "settings": {"account": "<PERSON><PERSON><PERSON>", "appearance": "<PERSON><PERSON><PERSON>", "notifications": "<PERSON><PERSON><PERSON><PERSON> báo", "privacy": "<PERSON><PERSON><PERSON><PERSON> riêng tư", "language": "<PERSON><PERSON><PERSON>", "about": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u"}, "errors": {"required": "Trư<PERSON>ng này là bắ<PERSON> buộc", "invalidEmail": "<PERSON><PERSON> h<PERSON> l<PERSON>", "passwordMismatch": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "minLength": "<PERSON><PERSON><PERSON> thiểu {{count}} ký tự", "maxLength": "T<PERSON>i đa {{count}} ký tự", "serverError": "Lỗi máy chủ, vui lòng thử lại sau", "networkError": "Lỗi kết n<PERSON>, vui lòng kiểm tra mạng của bạn"}, "validation": {"required": "{{field}} l<PERSON> b<PERSON><PERSON> b<PERSON>", "email": "<PERSON><PERSON> h<PERSON> l<PERSON>", "minLength": "{{field}} ph<PERSON>i có ít nhất {{length}} ký tự", "maxLength": "{{field}} kh<PERSON><PERSON> đ<PERSON><PERSON><PERSON> v<PERSON><PERSON> quá {{length}} ký tự", "phone": "<PERSON><PERSON> điện tho<PERSON>i phải có 10-15 chữ số", "passwordUppercase": "<PERSON><PERSON><PERSON> kh<PERSON>u phải chứa ít nhất một chữ hoa", "passwordLowercase": "<PERSON><PERSON><PERSON> khẩu phải chứa ít nhất một chữ thường", "passwordNumber": "<PERSON><PERSON><PERSON> khẩu phải chứa ít nhất một chữ số", "passwordSpecial": "<PERSON><PERSON>t khẩu phải chứa ít nhất một ký tự đặc biệt", "passwordsMatch": "<PERSON>ật khẩu và xác nhận mật khẩu không khớp", "emailOrPhone": "Email hoặc số điện tho<PERSON>i không hợp lệ"}, "components": {"library": {"title": "<PERSON><PERSON>ư viện Components", "description": "Thư viện các components dùng chung cho hệ thống RedAI. Các components được thiết kế theo phong cách hiện đại, hỗ trợ đầy đủ light/dark mode và responsive."}, "charts": {"demo": {"title": "Demo Biểu đồ", "description": "Các component biểu đồ hỗ trợ responsive, đa ngôn ngữ, và theme."}, "lineChart": {"title": "<PERSON><PERSON><PERSON><PERSON> đồ đường", "description": "LineChart component hiển thị dữ liệu dạng đường, hỗ trợ nhiều đường dữ liệu, tooltip, và legend.", "basic": {"title": "<PERSON><PERSON><PERSON><PERSON> đồ đư<PERSON><PERSON> c<PERSON> bản", "description": "<PERSON>i<PERSON>u đồ đường cơ bản với một đường dữ liệu."}, "multiLine": {"title": "<PERSON><PERSON><PERSON><PERSON> đồ nhiều đường", "description": "Biểu đồ đường với nhiều đường dữ liệu."}, "customized": {"title": "<PERSON><PERSON><PERSON>u đồ đường tùy chỉnh", "description": "Biểu đồ đường với các tùy chỉnh như loại đường, đ<PERSON> dày, và hiển thị điểm dữ liệu."}}}, "tooltip": {"title": "<PERSON><PERSON><PERSON>", "description": "Tooltip hiển thị thông tin bổ sung khi hover vào phần tử", "basic": {"title": "<PERSON><PERSON><PERSON> c<PERSON> bản", "description": "<PERSON>lt<PERSON> c<PERSON> bản với cài đặt mặc định.", "content": "Đây là một tooltip", "button": "Di chuột vào đây"}, "positions": {"title": "<PERSON><PERSON> trí <PERSON>", "description": "Tooltip có thể hiển thị ở các vị trí khác nhau.", "top": "<PERSON>lt<PERSON> phía trên", "topButton": "<PERSON><PERSON><PERSON><PERSON>", "right": "<PERSON><PERSON><PERSON> b<PERSON>n <PERSON>", "rightButton": "<PERSON><PERSON><PERSON>", "bottom": "<PERSON>lt<PERSON> ph<PERSON> d<PERSON>", "bottomButton": "Dư<PERSON><PERSON>", "left": "<PERSON><PERSON><PERSON> bên tr<PERSON>i", "leftButton": "Trái"}, "variants": {"title": "<PERSON><PERSON><PERSON><PERSON> thể <PERSON>", "description": "Tooltip với các kiểu dáng khác nhau.", "dark": {"content": "<PERSON><PERSON><PERSON> t<PERSON>i", "button": "<PERSON><PERSON><PERSON>"}, "light": {"content": "<PERSON><PERSON><PERSON> s<PERSON>", "button": "<PERSON><PERSON><PERSON>"}}, "sizes": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON>lt<PERSON> với các kích thước kh<PERSON>c nhau.", "small": {"content": "Tooltip nhỏ"}, "medium": {"content": "Tooltip vừa"}, "large": {"content": "Tooltip lớn"}}, "withIcons": {"title": "Tooltip với Icons", "description": "Toolt<PERSON> đ<PERSON><PERSON>c sử dụng với các nút icon.", "add": "<PERSON><PERSON><PERSON><PERSON> mục mới"}, "noArrow": {"title": "<PERSON><PERSON><PERSON> không có mũi tên", "description": "Tooltip có thể hiển thị không có mũi tên.", "content": "<PERSON><PERSON><PERSON> không có mũi tên", "button": "<PERSON><PERSON><PERSON><PERSON> mũi tên"}}, "searchBar": {"title": "Search Bar", "description": "<PERSON><PERSON> tìm kiếm với hiệu ứng animation và nhiều kiểu dáng khác nhau", "basic": {"title": "<PERSON><PERSON> tìm kiếm c<PERSON> bản", "description": "<PERSON><PERSON> tìm kiếm cơ bản với chức năng bật/tắt."}, "variants": {"title": "<PERSON><PERSON><PERSON> biến thể thanh tìm kiếm", "description": "<PERSON><PERSON> tìm kiếm với các kiểu dáng khác nhau."}, "animation": {"title": "<PERSON><PERSON><PERSON>h tìm kiếm", "description": "<PERSON><PERSON> tìm kiếm với hiệu <PERSON>ng bật/tắt."}, "withoutClear": {"title": "<PERSON><PERSON> tìm kiếm không có nút xóa", "description": "<PERSON><PERSON> tìm kiếm không có nút xóa."}, "customWidth": {"title": "<PERSON><PERSON> tìm kiếm với chiều rộng tùy chỉnh", "description": "<PERSON><PERSON> tìm kiếm với chiều rộng tối đa tùy chỉnh."}, "show": "<PERSON><PERSON><PERSON>", "hide": "Ẩn"}, "modernMenu": {"title": "Modern Menu", "description": "<PERSON>u hiện đại với nhiều kiểu dáng và vị trí khác nhau", "basic": {"title": "<PERSON><PERSON><PERSON> bản", "description": "<PERSON><PERSON> c<PERSON> bản với cài đặt mặc định."}, "withIcons": {"title": "Menu với Icons", "description": "Các mục menu với icons."}, "placement": {"title": "<PERSON><PERSON> trí <PERSON>", "description": "<PERSON><PERSON> với các vị trí khác nhau.", "top": "<PERSON><PERSON><PERSON><PERSON>", "right": "<PERSON><PERSON><PERSON>", "bottom": "Dư<PERSON><PERSON>", "left": "Trái"}}, "chips": {"title": "Chips", "description": "Chips là các phần tử nhỏ gọn thể hiện một đầu vào, thu<PERSON><PERSON> t<PERSON>, hoặc hành động", "basic": {"title": "<PERSON><PERSON> c<PERSON> bản", "description": "<PERSON>s cơ bản với các biến thể khác nhau."}, "outlined": {"title": "<PERSON>s viền ngo<PERSON>i", "description": "Chips viền ngoài với các biến thể khác nhau."}, "sizes": {"title": "<PERSON><PERSON><PERSON>", "description": "Chips với các kích thước kh<PERSON>c nhau."}, "icons": {"title": "Chips với Icons", "description": "Chips với icon bên tr<PERSON>i và phải."}, "closable": {"title": "<PERSON>s có thể đóng", "description": "Chips với nút đóng."}, "clickable": {"title": "<PERSON>s có thể nhấp", "description": "<PERSON>s có thể được nhấp vào."}, "disabled": {"title": "Chips vô hiệu hóa", "description": "Chips trong trạng thái vô hiệu hóa."}, "avatar": {"title": "Chips với Avatar", "description": "Chips với avatar."}, "loading": {"title": "<PERSON><PERSON> đang tải", "description": "Chips trong trạng thái đang tải."}, "selected": {"title": "<PERSON><PERSON> đ<PERSON> ch<PERSON>n", "description": "Chips trong trạng thái đã chọn."}, "animation": {"title": "<PERSON>s c<PERSON> hi<PERSON>u <PERSON>", "description": "Chips với c<PERSON>c hi<PERSON>u <PERSON>ng animation."}, "group": {"title": "Nhóm Chips", "description": "Nhóm các chips với nhiều tính năng khác nhau.", "basic": "Nhóm Chips c<PERSON> bản", "closable": "Nhóm Chips có thể đóng", "multiSelect": "Nhóm Chips chọn n<PERSON>u", "animated": "Nhóm Chips có hi<PERSON>u <PERSON>"}}, "menu": {"title": "<PERSON><PERSON>", "description": "<PERSON>u với nhiều tính năng: submenu, các mode khác nhau, collapsed state", "horizontal": {"title": "<PERSON><PERSON>u", "description": "<PERSON><PERSON> hi<PERSON>n thị theo chiều ngang"}, "vertical": {"title": "Vertical Menu", "description": "<PERSON><PERSON> hiển thị theo chi<PERSON>u dọc"}, "inline": {"title": "Inline Menu", "description": "<PERSON>u với submenu hiển thị inline"}, "variants": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> biến thể khác nhau của <PERSON>u"}, "advanced": {"title": "Advanced Menu Items", "description": "Menu items với badge và shortcut"}, "expand": "Mở rộng", "collapse": "<PERSON><PERSON>"}, "showCode": "Hiện code", "hideCode": "Ẩn code", "copied": "Đã sao chép!", "copy": "Sao chép", "categories": {"typography": {"title": "Typography", "description": "Các component định dạng văn bản để hiển thị nhất quán trong ứng dụng."}, "buttons": {"title": "Buttons", "description": "<PERSON><PERSON><PERSON> lo<PERSON> nút bấm khác nhau: primary, secondary, outline, icon buttons..."}, "cards": {"title": "Cards", "description": "<PERSON><PERSON><PERSON> card hiển thị nội dung, thông tin, d<PERSON> li<PERSON>u..."}, "chips": {"title": "Chips", "description": "Chips là các phần tử nhỏ gọn thể hiện một đầu vào, thu<PERSON><PERSON> t<PERSON>, hoặc hành động..."}, "inputs": {"title": "Inputs", "description": "<PERSON><PERSON><PERSON> input: text, number, checkbox, radio, select..."}, "layout": {"title": "Layout Components", "description": "Các component bố cục: container, grid, flex, resizer..."}, "theme": {"title": "Theme Components", "description": "Các component liên quan đến theme: theme toggle, language selector...", "system": {"title": "<PERSON><PERSON> thống Theme", "description": "<PERSON><PERSON> thống theme mới với khả năng tùy chỉnh và mở rộng"}}, "form": {"title": "Form Components", "description": "Các component form: input, select, checkbox, radio...", "theme": {"title": "Form với Theme System", "description": "Demo các component form sử dụng hệ thống theme mới"}}}, "buttons": {"title": "Buttons", "description": "<PERSON><PERSON><PERSON> lo<PERSON> nút bấm khác nhau đư<PERSON><PERSON> sử dụng trong hệ thống RedAI.", "variants": {"title": "Button Variants", "description": "<PERSON><PERSON><PERSON> biến thể của <PERSON>: primary, secondary, outline, success, warning, danger"}, "sizes": {"title": "Button Sizes", "description": "<PERSON><PERSON><PERSON> k<PERSON>ch th<PERSON><PERSON><PERSON> c<PERSON>: small, medium, large"}, "withIcons": {"title": "Button with Icons", "description": "<PERSON><PERSON> kết hợp với icon ở bên trái hoặc bên phải"}, "fullWidth": {"title": "Full Width <PERSON>", "description": "<PERSON><PERSON> chiếm toàn bộ chiều rộng của container", "button": "<PERSON><PERSON><PERSON> chiều rộng đầy đủ"}, "loading": {"title": "Loading <PERSON>ton", "description": "Button trong trạng thái loading"}, "disabled": {"title": "Disabled <PERSON><PERSON>", "description": "Button trong trạng thái disabled"}}, "grid": {"title": "Grid", "description": "Component <PERSON>rid gi<PERSON>p tạo layout dạng lưới linh hoạt và responsive.", "basic": {"title": "<PERSON><PERSON> <PERSON><PERSON> bản", "description": "<PERSON><PERSON> với số cột cố định"}, "responsive": {"title": "Grid Responsive", "description": "<PERSON>rid với số cột thay đổi theo kích thư<PERSON><PERSON> màn hình"}, "gaps": {"title": "Grid Gaps", "description": "<PERSON><PERSON> v<PERSON>i các k<PERSON>ng cách khác nhau gi<PERSON>a các phần tử", "small": "K<PERSON><PERSON>ng cách nhỏ", "medium": "K<PERSON>ảng c<PERSON>ch vừa", "large": "K<PERSON><PERSON>ng c<PERSON>ch lớn"}}, "banner": {"title": "Banner", "description": "Component <PERSON> hiển thị nội dung nổi bật với nhiều tùy chọn.", "basic": {"title": "<PERSON> c<PERSON> bản", "description": "Banner cơ bản với tiêu đề và mô tả."}, "withBackground": {"title": "Banner v<PERSON><PERSON> h<PERSON>nh <PERSON>n", "description": "Banner v<PERSON><PERSON> hình nền và overlay."}, "gradient": {"title": "Banner với gradient", "description": "Banner với nền gradient và các nút hành động."}, "wave": {"title": "Banner v<PERSON>i hiệu <PERSON> sóng", "description": "Banner với hiệu ứng sóng ở dưới cùng."}, "custom": {"title": "Banner với nội dung tùy chỉnh", "description": "Banner với nội dung tùy chỉnh thay vì sử dụng title và description."}}, "animation": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> hi<PERSON>u ứng động có sẵn trong RedAI Frontend Template.", "fadeSlide": {"title": "Hiệu ứng Fade & Slide"}, "fadeIn": "<PERSON><PERSON><PERSON> mờ d<PERSON>n", "fadeInAnimation": "<PERSON><PERSON><PERSON> mờ d<PERSON>n", "slideIn": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> v<PERSON>o", "slideInAnimation": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> v<PERSON>o", "slideInLeft": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> từ trái", "slideInLeftAnimation": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> từ trái", "slideInRight": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> từ phải", "slideInRightAnimation": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> từ phải", "durationTiming": {"title": "<PERSON><PERSON>ờ<PERSON> gian & <PERSON><PERSON>c độ"}, "fast": "Nhanh (200ms)", "fastAnimation": "<PERSON><PERSON><PERSON>", "medium": "Trung bình (500ms)", "mediumAnimation": "<PERSON><PERSON><PERSON>ng trung bình", "slow": "Chậm (1000ms)", "slowAnimation": "<PERSON><PERSON><PERSON>", "continuous": {"title": "<PERSON><PERSON><PERSON> liên tục"}, "pulse": "<PERSON><PERSON><PERSON><PERSON>", "pulseAnimation": "<PERSON><PERSON><PERSON><PERSON><PERSON> đ<PERSON>p", "spin": "Xoay", "component": {"title": "<PERSON><PERSON>u ứng Component"}, "tooltipContent": "Đây là tooltip với hiệu <PERSON>ng", "hoverMe": "<PERSON> qua", "openModal": "Mở Modal", "animatedModal": "<PERSON><PERSON> c<PERSON> hi<PERSON>", "modalDescription": "<PERSON><PERSON> này có hiệu ứng khi mở và đóng.", "infoNotification": "<PERSON>hông báo thông tin", "successNotification": "<PERSON><PERSON><PERSON><PERSON> báo thành công", "warningNotification": "<PERSON><PERSON><PERSON><PERSON> báo cảnh báo", "errorNotification": "<PERSON>h<PERSON>ng báo lỗi", "notificationDescription": "<PERSON><PERSON><PERSON><PERSON> báo này có hiệu ứng khi xuất hiện và biến mất."}, "responsiveGrid": {"title": "Responsive Grid", "description": "Grid responsive nâng cao tự động điều chỉnh dựa trên kích thước màn hình và trạng thái chat panel.", "basic": {"title": "Responsive <PERSON><PERSON> c<PERSON> bản", "description": "Responsive grid với cài đặt mặc định"}, "customColumns": {"title": "<PERSON><PERSON><PERSON> chỉnh số cột", "description": "Responsive grid với số cột tùy chỉnh"}, "customGaps": {"title": "<PERSON><PERSON><PERSON> chỉnh khoảng cách", "description": "Responsive grid với k<PERSON>ng cách tùy chỉnh"}, "withChatPanel": {"title": "Với Chat Panel", "description": "Responsive grid tự động điều chỉnh khi chat panel được mở hoặc đóng"}, "realWorld": {"title": "<PERSON><PERSON> dụ thực tế", "description": "<PERSON><PERSON> dụ về responsive grid đ<PERSON><PERSON>c sử dụng trong module AI Agents"}, "currentColumns": "<PERSON><PERSON> cột hiện tại", "chatPanel": "Chat panel", "open": "Mở", "closed": "Đ<PERSON><PERSON>", "openChatPanel": "Mở Chat Panel", "closeChatPanel": "<PERSON><PERSON><PERSON>", "props": "<PERSON><PERSON><PERSON><PERSON>", "propName": "<PERSON><PERSON><PERSON><PERSON>", "propType": "<PERSON><PERSON><PERSON> dữ liệu", "propDefault": "Mặc định", "propDescription": "<PERSON><PERSON>", "childrenDescription": "<PERSON><PERSON><PERSON> phần tử con hiển thị trong grid", "gapDescription": "<PERSON><PERSON><PERSON><PERSON> cách gi<PERSON><PERSON> các phần tử trong grid", "maxColumnsDescription": "<PERSON><PERSON> cột tối đa cho mỗi breakpoint khi chat panel đóng", "maxColumnsWithChatPanelDescription": "<PERSON><PERSON> cột tối đa cho mỗi breakpoint khi chat panel mở", "classNameDescription": "<PERSON><PERSON><PERSON> CSS b<PERSON> sung", "onColumnsChangeDescription": "Callback khi số cột thay đổi"}, "cards": {"title": "Cards", "description": "<PERSON><PERSON><PERSON> card hiển thị nội dung, thông tin, dữ li<PERSON>u trong hệ thống RedAI.", "basic": {"title": "<PERSON> c<PERSON> bản", "description": "Card cơ bản với tiêu đề và nội dung"}, "withFooter": {"title": "<PERSON> v<PERSON><PERSON>", "description": "<PERSON> có phần footer ch<PERSON><PERSON> c<PERSON> action"}, "withIcon": {"title": "Card với Icon", "description": "Card có icon ở header"}, "bordered": {"title": "<PERSON> có viền", "description": "Card có viền xung quanh"}, "customHeader": {"title": "Card với Header tùy chỉnh", "description": "Card có <PERSON> đ<PERSON><PERSON><PERSON> tùy chỉnh"}}, "typography": {"title": "Typography", "description": "Các component định dạng văn bản để hiển thị nhất quán trong ứng dụng.", "headings": {"title": "Headings", "description": "<PERSON><PERSON><PERSON> biến thể heading từ h1 đến h6."}, "body": {"title": "Body Text", "description": "<PERSON><PERSON><PERSON> biến thể văn bản cho đoạn văn và nội dung chung."}, "colors": {"title": "<PERSON><PERSON><PERSON> chữ", "description": "Typography với các tùy chọn màu khác nhau."}, "weights": {"title": "<PERSON><PERSON> đậm chữ", "description": "Typography với các độ đậm chữ khác nhau."}, "alignment": {"title": "<PERSON><PERSON>n chỉnh văn bản", "description": "Typography với các kiểu căn chỉnh văn bản khác nhau."}, "truncation": {"title": "<PERSON><PERSON><PERSON> ng<PERSON>n văn bản", "description": "Typography với t<PERSON>h năng cắt ngắn cho văn bản dài."}, "responsive": {"title": "Typography đ<PERSON><PERSON>ng", "description": "Typography v<PERSON>i kích thư<PERSON>c chữ thay đổi theo màn hình."}}, "theme": {"title": "Theme Components", "description": "Các component liên quan đến theme và ngôn ngữ trong hệ thống RedAI.", "toggle": {"title": "Theme Toggle", "description": "Toggle chuyển đổi gi<PERSON>a light mode và dark mode"}, "toggleSizes": {"title": "Theme Toggle Sizes", "description": "Theme Toggle với các kích thước khác nhau"}, "toggleCustomText": {"title": "Theme Toggle with Custom Text", "description": "Theme Toggle với text tùy chỉnh"}, "languageFlags": {"title": "Language Flags", "description": "Hi<PERSON>n thị cờ của các ngôn ngữ được hỗ trợ"}, "languageFlagSizes": {"title": "Language Flag Sizes", "description": "Language Flag với các kích thước khác nhau"}, "system": {"title": "<PERSON><PERSON> thống Theme", "description": "<PERSON><PERSON> thống theme mới với khả năng tùy chỉnh và mở rộng", "variables": {"title": "Biến CSS", "description": "<PERSON><PERSON><PERSON> CSS đư<PERSON>c sử dụng trong hệ thống theme", "primaryColor": "<PERSON><PERSON><PERSON>", "primaryDescription": "Sử dụng màu primary và primary-foreground", "secondaryColor": "<PERSON><PERSON><PERSON> phụ", "secondaryDescription": "Sử dụng màu secondary và secondary-foreground", "accentColor": "<PERSON><PERSON><PERSON>", "accentDescription": "Sử dụng màu accent và accent-foreground", "successColor": "<PERSON><PERSON><PERSON> thành công", "successDescription": "Sử dụng màu success và success-foreground", "warningColor": "<PERSON><PERSON><PERSON> b<PERSON>o", "warningDescription": "Sử dụng màu warning và warning-foreground", "errorColor": "M<PERSON><PERSON> lỗi", "errorDescription": "Sử dụng màu error và error-foreground"}, "cards": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> k<PERSON> card sử dụng hệ thống theme mới", "content": "Nội dung card sử dụng biến theme", "mutedTitle": "Card <PERSON>", "mutedContent": "Sử dụng nền card-muted"}, "typography": {"title": "Typography", "description": "Typography sử dụng hệ thống theme mới"}}, "customizer": {"title": "Theme Customizer", "description": "Component cho phép người dùng tùy chỉnh theme", "open": "Mở Theme Customizer", "mode": "Chế độ Theme", "primaryColor": "<PERSON><PERSON><PERSON>", "secondaryColor": "<PERSON><PERSON><PERSON> phụ", "backgroundColor": "<PERSON><PERSON><PERSON>", "textColor": "<PERSON><PERSON><PERSON> chữ", "borderRadius": "<PERSON>"}, "languageFlagWithLabel": {"title": "Language Flag with Label", "description": "Language Flag kèm theo tên ngôn ngữ"}}, "inputs": {"title": "Inputs", "description": "<PERSON><PERSON><PERSON> input dùng để nhập liệu trong hệ thống RedAI.", "text": {"title": "Text Input", "description": "Input c<PERSON> bản để nhập text"}, "password": {"title": "Password Input", "description": "Input để nhập mật khẩu với toggle hiển thị"}, "helperText": {"title": "Input with Helper Text", "description": "Input có text hỗ trợ bên dưới"}, "error": {"title": "Input with Error", "description": "Input trong trạng thái lỗi"}, "disabled": {"title": "Disabled Input", "description": "Input trong trạng thái disabled"}, "withIcon": {"title": "Input with Icon", "description": "Input có icon bên trái hoặc phải"}, "labels": {"username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "password": "<PERSON><PERSON><PERSON>", "email": "Email", "search": "<PERSON><PERSON><PERSON>", "website": "Website"}, "placeholders": {"username": "<PERSON><PERSON><PERSON><PERSON> tên đăng nhập", "password": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u", "email": "Nhập email", "search": "<PERSON><PERSON><PERSON> k<PERSON>...", "website": "Nhập URL website"}, "helpers": {"emailPrivacy": "<PERSON><PERSON>g tôi sẽ không bao giờ chia sẻ email của bạn với bất kỳ ai khác."}, "errors": {"invalidEmail": "<PERSON><PERSON> lòng nhập địa chỉ email hợp lệ"}, "select": {"title": "Select", "description": "Component select nâng cao với nhi<PERSON>u t<PERSON>h năng"}, "singleSelect": {"title": "Single Select", "description": "Select cho phép chọn một giá trị"}, "multiSelect": {"title": "Multi Select", "description": "Select cho phép chọn nhiều giá trị"}, "searchableSelect": {"title": "Searchable Select", "description": "Select có tính năng tìm kiếm"}, "groupedSelect": {"title": "Grouped Select", "description": "Select với options đ<PERSON><PERSON><PERSON> nhóm lại"}, "loadingSelect": {"title": "Loading Select", "description": "Select trong trạng thái loading"}, "customRenderingSelect": {"title": "Custom Rendering Select", "description": "Select với custom rendering của options"}, "asyncSelect": {"title": "Async Select", "description": "Select với khả năng tải dữ liệu từ API"}, "creatableSelect": {"title": "Creatable Select", "description": "Select cho phép tạo option mới nếu không tìm thấy"}, "comboboxSelect": {"title": "Combobox Select", "description": "Select kết hợp giữa dropdown và input, cho phép nhập tự do"}, "typeaheadSelect": {"title": "Typeahead Select", "description": "Select với gợi ý khi gõ"}, "datepicker": {"title": "DatePicker Component", "description": "Date picker n<PERSON><PERSON> cao với chọn ngày đơn và chọn k<PERSON>ng"}, "checkboxRadio": {"title": "Checkbox & Radio Components", "description": "Checkbox, CheckboxGroup, Radio, và RadioGroup components"}}, "layout": {"title": "Layout Components", "description": "Các component bố cục dùng để tổ chức layout trong hệ thống RedAI.", "container": {"title": "Container", "description": "Container g<PERSON><PERSON><PERSON> hạn chiều rộng nội dung và căn giữa"}, "grid": {"title": "Grid", "description": "Grid layout với số cột tùy chỉnh"}, "responsiveGrid": {"title": "Responsive Grid", "description": "Grid layout với số cột thay đổi theo kích thư<PERSON><PERSON> màn hình"}, "resizer": {"title": "Resizer", "description": "Component cho phép thay đổi kích thư<PERSON><PERSON> gi<PERSON>a hai panel"}, "horizontalResizer": {"title": "Horizontal Resizer", "description": "Resizer theo chi<PERSON> ngang"}, "panels": {"left": "Panel trái", "right": "Panel phải", "top": "<PERSON> trên", "bottom": "Panel dưới"}}, "form": {"title": "Form Components", "description": "Bộ sưu tập các component form để xây dựng form với nhiều layout và hành vi khác nhau.", "basic": {"title": "Form Cơ bản", "description": "Form cơ bản với validation sử dụng Zod schema.", "example1": {"title": "Form Cơ bản", "description": "Form đơn giản với validation cơ bản sử dụng Zod schema."}, "example2": {"title": "Form với Validation Nâng cao", "description": "Form với các quy tắc validation nâng cao sử dụng Zod schema."}, "fields": {"name": "<PERSON><PERSON><PERSON>", "email": "Email", "username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "password": "<PERSON><PERSON><PERSON>", "confirmPassword": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u"}, "placeholders": {"name": "<PERSON><PERSON><PERSON><PERSON> tên của bạn", "email": "Nhập email c<PERSON><PERSON> bạn", "username": "<PERSON><PERSON><PERSON><PERSON> tên đăng nhập", "password": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u", "confirmPassword": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u"}, "helpTexts": {"agreeTerms": "T<PERSON>i đồng ý với các điều khoản và điều kiện"}, "buttons": {"submit": "<PERSON><PERSON><PERSON>", "register": "<PERSON><PERSON><PERSON> ký"}, "result": "<PERSON><PERSON><PERSON> qu<PERSON>", "reference": {"title": "<PERSON><PERSON><PERSON> liệu tham khảo Form Components", "form": {"title": "Form Component", "description": "Component ch<PERSON><PERSON> quản lý trạng thái và validation của form."}, "formItem": {"title": "FormItem Component", "description": "<PERSON><PERSON> bọc các trường input với label, thông báo lỗi và text hỗ trợ."}, "input": {"title": "Input Component", "description": "Component input c<PERSON> bản với nhiều tùy chọn."}}}, "dependencies": {"title": "Form Field Dependencies", "description": "<PERSON><PERSON><PERSON> c<PERSON>c tr<PERSON><PERSON><PERSON> phụ thuộc nh<PERSON> dropdown theo c<PERSON><PERSON> bậc"}, "templates": {"title": "Form Templates", "description": "Các mẫu form sẵn sàng sử dụng cho các trường hợp phổ biến"}, "array": {"title": "Form Arrays", "description": "Các trường form động với chức năng thêm/xóa"}, "labels": {"name": "<PERSON><PERSON><PERSON>", "email": "Email", "country": "Quốc gia", "gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "agreeTerms": "T<PERSON>i đồng ý với các điều khoản và điều kiện", "username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "password": "<PERSON><PERSON><PERSON>"}, "placeholders": {"name": "<PERSON><PERSON><PERSON><PERSON> tên của bạn", "email": "Nhập email c<PERSON><PERSON> bạn", "country": "<PERSON><PERSON><PERSON> quốc gia của bạn", "password": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u của bạn"}, "helpers": {"passwordLength": "Phải có ít nhất 8 ký tự"}, "errors": {"usernameTaken": "<PERSON><PERSON><PERSON> đăng nhập đã được sử dụng", "selectCountry": "<PERSON><PERSON> lòng chọn quốc gia"}, "countries": {"us": "<PERSON>a Kỳ", "uk": "<PERSON><PERSON><PERSON><PERSON>", "ca": "Canada", "au": "Úc", "de": "<PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON>", "jp": "<PERSON><PERSON><PERSON><PERSON>", "cn": "<PERSON><PERSON>", "in": "Ấn Độ", "br": "Brazil"}, "gender": {"male": "Nam", "female": "<PERSON><PERSON>", "other": "K<PERSON><PERSON><PERSON>"}, "grid": {"title": "Form Grid Layout", "description": "Tổ chức các field trong form theo layout dạng lưới responsive."}, "inline": {"title": "Form Inline Layout", "description": "Tạo form inline cho tìm kiếm hoặc các input đơn giản."}, "horizontal": {"title": "Form Horizontal Layout", "description": "Tạo form với label bên trái và field bên phải."}, "layout": {"title": "Form Layouts", "description": "<PERSON><PERSON><PERSON> tùy chọn layout khác nhau để tổ chức các trường form.", "grid": {"title": "Grid Layout", "description": "<PERSON><PERSON> chức các trư<PERSON> form theo layout dạng lưới responsive."}, "inline": {"title": "Inline Layout", "description": "Tạo form inline cho thanh tìm kiếm và bộ lọc."}, "horizontal": {"title": "Horizontal Layout", "description": "Tạo form với label bên trái và trường bên phải."}, "fields": {"firstName": "<PERSON><PERSON><PERSON>", "lastName": "Họ", "email": "Email", "phone": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i", "address": "Địa chỉ", "city": "<PERSON><PERSON><PERSON><PERSON> phố", "state": "Tỉnh/Thành", "zipCode": "<PERSON><PERSON> b<PERSON>u đi<PERSON>n", "search": "<PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "password": "<PERSON><PERSON><PERSON>"}, "placeholders": {"firstName": "<PERSON><PERSON><PERSON><PERSON> tên", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "email": "Nhập email", "phone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "address": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "city": "<PERSON><PERSON><PERSON><PERSON> thành phố", "state": "Nhập tỉnh/thành", "zipCode": "<PERSON><PERSON><PERSON><PERSON> mã b<PERSON>u đi<PERSON>n", "search": "<PERSON><PERSON><PERSON> k<PERSON>...", "username": "<PERSON><PERSON><PERSON><PERSON> tên đăng nhập", "password": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u"}, "options": {"allCategories": "<PERSON><PERSON><PERSON> cả danh mục", "products": "<PERSON><PERSON><PERSON> p<PERSON>m", "services": "<PERSON><PERSON><PERSON> v<PERSON>", "blogs": "<PERSON><PERSON><PERSON> vi<PERSON>"}, "buttons": {"submit": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "login": "<PERSON><PERSON><PERSON>"}, "result": "<PERSON><PERSON><PERSON> qu<PERSON>", "reference": {"title": "<PERSON><PERSON><PERSON> li<PERSON>u tham khảo Form Layout Components", "formGrid": {"title": "FormGrid Component", "description": "Tạo layout dạng lưới responsive cho các trường form."}, "formInline": {"title": "FormInline Component", "description": "Tạo layout inline cho các trường form."}, "formHorizontal": {"title": "FormHorizontal Component", "description": "Tạo layout ngang với label bên trái và trường bên phải."}}}, "conditional": {"title": "Conditional Fields", "description": "Hiển thị hoặc ẩn các field dựa trên điều kiện.", "basic": {"title": "Conditional Fields <PERSON><PERSON> bản", "description": "<PERSON><PERSON><PERSON> thị các trường dựa trên điều kiện đơn giản."}, "advanced": {"title": "Conditional Fields Nâng cao", "description": "<PERSON><PERSON> dụng các điều kiện phức tạp với logic AND/OR."}, "component": {"title": "ConditionalField Component", "description": "ConditionalField component cho phép bạn hiển thị hoặc ẩn các trường form dựa trên điều kiện. <PERSON><PERSON> hữu ích để tạo các form động thích ứng với đầu vào của người dùng.", "basicUsage": {"title": "Sử dụ<PERSON> bản", "description": "Tr<PERSON><PERSON><PERSON> hợp sử dụng phổ biến nhất là hiển thị các trường dựa trên một điều kiện đơn giản:"}, "conditionTypes": {"title": "<PERSON><PERSON><PERSON>", "description": "ConditionalField component hỗ trợ nhiều loại điều kiện khác nhau:"}}, "complex": {"title": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON>", "description": "ConditionalField component hỗ trợ các điều kiện phức tạp sử dụng logic AND và OR. Điều này cho phép bạn tạo các hành vi form phức tạp.", "and": {"title": "Logic AND", "description": "Sử dụng logic AND khi bạn muốn hiển thị các trường chỉ khi nhiều điều kiện được đáp <PERSON>ng:"}, "or": {"title": "Logic OR", "description": "Sử dụng logic OR khi bạn muốn hiển thị các trường khi bất kỳ điều kiện nào đư<PERSON><PERSON> đ<PERSON>:"}}, "fields": {"accountType": "<PERSON><PERSON><PERSON> tà<PERSON>", "name": "<PERSON><PERSON><PERSON>", "email": "Email", "companyName": "<PERSON><PERSON>n công ty", "taxId": "<PERSON><PERSON> số thuế", "differentBillingAddress": "Đ<PERSON>a chỉ thanh toán kh<PERSON>?", "billingAddress": "<PERSON><PERSON><PERSON> chỉ thanh toán", "city": "<PERSON><PERSON><PERSON><PERSON> phố", "hasSpecialRequirements": "<PERSON><PERSON><PERSON> cầu đặc biệt?", "specialRequirements": "<PERSON><PERSON><PERSON> cầu đặc biệt", "contactMethod": "<PERSON><PERSON><PERSON><PERSON> thức liên hệ", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i"}, "placeholders": {"name": "<PERSON><PERSON><PERSON><PERSON> tên của bạn", "email": "Nhập email c<PERSON><PERSON> bạn", "companyName": "<PERSON><PERSON><PERSON><PERSON> tên công ty", "taxId": "<PERSON><PERSON><PERSON><PERSON> mã số thuế", "billingAddress": "<PERSON><PERSON><PERSON><PERSON> địa chỉ thanh toán", "city": "<PERSON><PERSON><PERSON><PERSON> thành phố", "specialRequirements": "<PERSON><PERSON><PERSON><PERSON> yêu cầu đặc biệt", "phoneNumber": "<PERSON><PERSON><PERSON><PERSON> số điện thoại"}, "options": {"personal": "Cá nhân", "business": "<PERSON><PERSON><PERSON>", "email": "Email", "phone": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i", "both": "<PERSON><PERSON> hai"}, "conditionTypes": {"equals": "Gi<PERSON> trị trường bằng một giá trị cụ thể", "notEquals": "Gi<PERSON> trị trường không bằng một giá trị cụ thể", "isTrue": "<PERSON><PERSON><PERSON> trị trường là true (cho trường boolean)", "isFalse": "<PERSON><PERSON><PERSON> trị trường là false (cho trườ<PERSON> boolean)", "isEmpty": "<PERSON><PERSON><PERSON> trị trường rỗng", "isNotEmpty": "<PERSON><PERSON><PERSON> trị trường không rỗng"}, "examples": {"business": "<PERSON><PERSON><PERSON> thị các trường công ty chỉ khi loại tài khoản là \"doanh nghiệp\"", "shipping": "Hiển thị địa chỉ giao hàng chỉ khi \"địa chỉ giao hàng khác\" đ<PERSON><PERSON><PERSON> chọn", "additional": "<PERSON><PERSON><PERSON> thị các tr<PERSON><PERSON><PERSON> b<PERSON> sung dựa trên lựa chọn"}, "buttons": {"submit": "<PERSON><PERSON><PERSON>"}, "result": "<PERSON><PERSON><PERSON> qu<PERSON>"}, "apiForm": {"title": "API Form Integration", "description": "Tích hợp form với API sử dụng hook useApiForm."}, "apiError": {"title": "Form API Error Handling", "description": "Xử lý lỗi API trong form sử dụng hook useFormErrors."}, "theme": {"title": "Form với Theme System", "description": "Demo các component form sử dụng hệ thống theme mới"}, "themeDemo": {"title": "Form Components với Theme System", "description": "Demo các component form sử dụng hệ thống theme mới"}, "basicForm": {"title": "Form Cơ bản", "description": "Form cơ bản với các input khác nhau"}, "examples": {"title": "Form Examples", "description": "<PERSON>ác ví dụ form hoàn chỉnh cho các trường hợp sử dụng phổ biến.", "login": {"title": "Form Đăng nhập & <PERSON><PERSON><PERSON> ký", "description": "<PERSON>ác ví dụ hoàn chỉnh về form đăng nhập và đăng ký với validation."}, "multistep": {"title": "Form Nhiều bước", "description": "Ví dụ về form nhiều bước với theo dõi tiến trình và validation."}, "datepicker": {"title": "<PERSON><PERSON> dụ DatePicker Nâng cao", "description": "<PERSON><PERSON> dụ thực tế về sử dụng DatePicker bao gồm ngày làm việ<PERSON>, ngày lễ và tích hợp form."}, "checkboxRadio": {"title": "Ví dụ Checkbox & Radio", "description": "Ví dụ về Checkbox và Radio components với các trạng thái, kích thước và layout khác nhau."}}, "demo": {"title": "<PERSON><PERSON> d<PERSON>", "description": "Các ví dụ hoàn chỉnh về form với validation sử dụng React Hook Form và Zod.", "result": "<PERSON><PERSON><PERSON> qu<PERSON>", "login": {"title": "Form Đăng nhập", "description": "Form đăng nhập hoàn chỉnh với validation và xử lý lỗi.", "emailHelp": "<PERSON>hập email đã đăng ký"}, "register": {"title": "Form Đăng ký", "description": "Form đăng ký hoàn chỉnh với các quy tắc validation nâng cao.", "passwordHelp": "<PERSON><PERSON><PERSON> khẩu phải có ít nhất 8 ký tự, bao gồ<PERSON> chữ hoa, chữ thườ<PERSON>, số và ký tự đặc biệt", "confirmPasswordPlaceholder": "<PERSON><PERSON><PERSON><PERSON> lại mật kh<PERSON>u"}, "bestPractices": {"title": "<PERSON><PERSON><PERSON> tắc <PERSON> Tốt nhất", "validation": {"title": "Validation Form", "description": "<PERSON><PERSON><PERSON> x<PERSON>c thực dữ liệu người dùng nhập cả ở phía client và server.", "item1": "Sử dụng <PERSON>od cho schema validation", "item2": "<PERSON><PERSON> cấp thông báo lỗi rõ ràng", "item3": "<PERSON><PERSON><PERSON> thực theo thời gian thực khi có thể"}, "accessibility": {"title": "<PERSON><PERSON><PERSON> n<PERSON> cận", "description": "<PERSON><PERSON>m bảo form của bạn có thể tiếp cận đư<PERSON><PERSON> với tất cả người dùng.", "item1": "Sử dụng label phù hợp cho tất cả các trường form", "item2": "<PERSON><PERSON><PERSON> bảo điều hướng bàn phím hoạt động tốt", "item3": "<PERSON><PERSON> dụng thu<PERSON><PERSON> t<PERSON>h <PERSON> khi cần thiết"}, "ux": {"title": "<PERSON><PERSON><PERSON><PERSON> nghi<PERSON> dùng", "description": "<PERSON><PERSON><PERSON> tr<PERSON>i nghiệm người dùng mượt mà.", "item1": "<PERSON><PERSON><PERSON> thị trạng thái loading trong quá trình gửi form", "item2": "<PERSON><PERSON> hiệu hóa nút gửi khi form không hợp lệ", "item3": "<PERSON><PERSON> cấp phản hồi thành công và lỗi rõ ràng"}}}, "validation": {"uppercase": "<PERSON><PERSON><PERSON> khẩu ph<PERSON>i có ít nhất 1 chữ hoa", "lowercase": "<PERSON><PERSON><PERSON> khẩu phải có ít nhất 1 chữ thường", "number": "<PERSON>ật khẩu ph<PERSON>i có ít nhất 1 số", "special": "<PERSON>ật khẩu phải có ít nhất 1 ký tự đặc biệt", "agreeTerms": "Bạn phải đồng ý với điều khoản sử dụng"}, "variants": {"title": "Form Component Variants", "description": "<PERSON><PERSON><PERSON> biến thể khác nhau của form components", "inputError": {"title": "Input với lỗi"}, "inputHelper": {"title": "Input với text hỗ trợ"}, "checkbox": {"title": "<PERSON><PERSON><PERSON> bi<PERSON>n thể Checkbox", "default": "Checkbox mặc định", "rounded": "Checkbox bo tròn", "filled": "Checkbox đầy đủ", "outlined": "Checkbox viền"}, "radio": {"title": "<PERSON><PERSON><PERSON>n thể <PERSON>", "default": "Radio mặc định", "filled": "Radio đầy đủ", "outlined": "Radio viền"}, "selectError": {"title": "Select với lỗi"}}}, "formDependencies": {"title": "Form Field Dependencies", "description": "Quản lý dependencies gi<PERSON>a các field trong form để tạo form động.", "transform": {"title": "Transform Dependencies", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t giá trị field dựa trên giá trị của field kh<PERSON><PERSON> thông qua hàm transform."}, "cascadingSelect": {"title": "Cascading Select", "description": "<PERSON>ập nhật options của select dựa trên giá trị của select kh<PERSON><PERSON>."}, "resetFields": {"title": "Reset Fields", "description": "Reset giá trị các field khi field khác thay đổi."}}, "formSections": {"title": "Form Sections", "description": "Chia form thành các section để tổ chức form rõ ràng và dễ sử dụng.", "collapsible": {"title": "Collapsible Sections", "description": "Section có thể đóng/mở để tiết kiệm không gian và tập trung vào phần đang làm việc."}, "nestedSections": {"title": "Nested Sections", "description": "Các section lồng nhau để tổ chức form phức tạp."}, "customStyling": {"title": "Custom Styling", "description": "Tùy chỉnh style cho section thông qua className."}, "variants": {"title": "Biến thể Section", "description": "<PERSON><PERSON><PERSON> biến thể khác nhau của FormSection.", "default": "Mặc định", "defaultDesc": "<PERSON><PERSON><PERSON> là biến thể mặc định", "bordered": "<PERSON><PERSON><PERSON><PERSON> đậm", "borderedDesc": "<PERSON><PERSON><PERSON> là biến thể có viền dày hơn", "elevated": "<PERSON><PERSON><PERSON> b<PERSON>t", "elevatedDesc": "<PERSON><PERSON><PERSON> là biến thể có hiệu ứng đổ bóng", "gradient": "Gradient", "gradientDesc": "<PERSON><PERSON><PERSON> là biến thể có nền gradient"}, "sizes": {"title": "Kích thước Section", "description": "<PERSON><PERSON><PERSON> k<PERSON>ch th<PERSON><PERSON><PERSON> khác nhau của FormSection.", "small": "Nhỏ", "smallDesc": "Đây là section kích thước nhỏ", "medium": "<PERSON>rung bình", "mediumDesc": "Đ<PERSON>y là section kích thước trung bình (mặc định)", "large": "Lớn", "largeDesc": "Đây là section kích thước lớn"}, "animation": {"title": "Animation", "description": "Hiệu ứng animation khi đóng/mở section.", "fade": "Fade", "fadeDesc": "<PERSON><PERSON><PERSON> ứng mờ dần khi đóng/mở", "slide": "Slide", "slideDesc": "<PERSON><PERSON><PERSON> <PERSON>ng tr<PERSON><PERSON><PERSON> lên/xuống khi đóng/mở", "both": "<PERSON><PERSON> hai", "bothDesc": "<PERSON><PERSON><PERSON> hợp cả hiệu ứng mờ dần và trư<PERSON>t"}, "accordion": {"title": "Accordion", "description": "Chỉ mở một section tại một thời điểm.", "group1": "Nhóm 1", "group2": "Nhóm 2", "firstSection": "Section đầu tiên trong accordion", "secondSection": "Section thứ hai trong accordion", "thirdSection": "Section thứ ba trong accordion"}, "badge": {"title": "Badge", "description": "Hiển thị badge bên cạnh tiêu đề.", "new": "<PERSON><PERSON><PERSON>", "required": "<PERSON><PERSON><PERSON> b<PERSON>", "optional": "<PERSON><PERSON><PERSON>", "stringBadgeTitle": "Section với String Badge", "stringBadgeDesc": "Section này có badge dạng chuỗi", "customBadgeTitle": "Section với Custom Badge", "customBadgeDesc": "Section này có badge dạng component tùy chỉnh"}, "iconPosition": {"title": "<PERSON><PERSON> trí <PERSON>", "description": "<PERSON><PERSON> trí của icon trong header.", "left": "Trái", "leftDesc": "Icon ở bên trái", "right": "<PERSON><PERSON><PERSON>", "rightDesc": "Icon ở bên <PERSON> (mặc định)"}, "fields": {"firstName": "<PERSON><PERSON><PERSON>", "lastName": "Họ", "email": "Email", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "companyName": "<PERSON><PERSON>n công ty", "jobTitle": "<PERSON><PERSON><PERSON> v<PERSON>", "department": "Phòng ban", "address": "Địa chỉ", "city": "<PERSON><PERSON><PERSON><PERSON> phố", "state": "Tỉnh/Thành phố", "zipCode": "<PERSON><PERSON> b<PERSON>u đi<PERSON>n", "country": "Quốc gia", "cardNumber": "Số thẻ", "cardName": "Tên chủ thẻ", "cardExpiry": "<PERSON><PERSON><PERSON> h<PERSON> hạn", "cardCvv": "Mã CVV", "saveCard": "<PERSON><PERSON><PERSON> thông tin thẻ", "receiveNewsletter": "<PERSON><PERSON><PERSON><PERSON> bản tin"}, "placeholders": {"firstName": "<PERSON><PERSON><PERSON><PERSON> tên của bạn", "lastName": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> của bạn", "email": "<EMAIL>", "phone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại của bạn", "companyName": "<PERSON><PERSON><PERSON><PERSON> tên công ty của bạn", "jobTitle": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON> v<PERSON> c<PERSON>a bạn", "department": "<PERSON><PERSON><PERSON><PERSON> phòng ban của bạn"}, "buttons": {"submit": "<PERSON><PERSON><PERSON>"}, "result": "<PERSON><PERSON><PERSON> qu<PERSON>", "personalInfo": "Thông tin cá nhân", "personalInfoDesc": "<PERSON><PERSON><PERSON><PERSON> thông tin cá nhân của bạn", "companyInfo": "Thông tin công ty", "companyInfoDesc": "<PERSON><PERSON><PERSON><PERSON> thông tin công ty của bạn (nếu có)", "formSectionComponent": "FormSection Component", "formSectionDesc": "Component FormSection dùng để nhóm các field trong form thành các section, giúp tổ chức form một cách rõ ràng và dễ sử dụng.", "features": "<PERSON><PERSON><PERSON>", "featuresList": {"groupFields": "Nhóm các field trong form thành các section", "collapsible": "Hỗ trợ collapsible (có thể đóng/mở)", "titleDesc": "Hiển thị tiêu đề và mô tả cho section", "variants": "<PERSON><PERSON><PERSON><PERSON> biến thể: default, bordered, elevated, gradient", "sizes": "<PERSON><PERSON><PERSON><PERSON> kích thước: sm, md, lg", "animation": "Hiệu <PERSON>ng animation khi đóng/mở: fade, slide, both", "accordion": "Hỗ trợ accordion (chỉ mở một section tại một thời điểm)", "badge": "Hỗ trợ badge bên cạnh tiêu đề", "iconPosition": "Tùy chỉnh vị trí icon: left, right", "customStyle": "Tùy chỉnh style thông qua className", "darkMode": "Hỗ trợ đầy đủ light/dark mode", "responsive": "Responsive trên mọi kích thư<PERSON>c màn hình"}, "props": "Props", "propsList": {"title": "Tiêu đề của section", "description": "Mô tả của section (optional)", "collapsible": "<PERSON><PERSON> thể đóng/mở section hay không (default: false)", "defaultExpanded": "Trạng thái mặc định của section (default: true)", "variant": "Biến thể của section (default, bordered, elevated, gradient)", "size": "<PERSON><PERSON><PERSON> th<PERSON> của section (sm, md, lg)", "icon": "Icon tùy chỉnh cho header", "iconPosition": "<PERSON>ị trí của icon (left, right)", "badge": "Badge hiển thị bên cạnh tiêu đề", "animated": "<PERSON><PERSON> sử dụng animation khi đóng/mở hay không", "animationType": "Loại animation (fade, slide, both)", "animationDuration": "Thời gian animation (ms)", "accordionId": "ID của accordion group", "id": "ID của section (dùng cho accordion)", "onExpandChange": "Callback khi trạng thái đóng/mở thay đổi", "className": "Class bổ sung cho section", "titleClassName": "Class bổ sung cho tiêu đề", "descriptionClassName": "Class bổ sung cho mô tả", "contentClassName": "Class bổ sung cho nội dung", "headerClassName": "Class bổ sung cho header"}, "collapsibleSections": "Collapsible Sections", "collapsibleDesc": "FormSection hỗ trợ tính năng collapsible (có thể đóng/mở) để tiết kiệm không gian và giúp người dùng tập trung vào phần đang làm việc.", "usage": "<PERSON><PERSON><PERSON> sử dụng", "usageSteps": {"addProp": "Thêm prop collapsible vào FormSection", "defaultState": "Sử dụng defaultExpanded để thiết lập trạng thái mặc định", "trackChanges": "<PERSON><PERSON> dụng onExpand<PERSON><PERSON>e để theo dõi thay đổi trạng thái"}, "managingSections": "Quản lý nhiều section", "managingSectionsDesc": "<PERSON><PERSON> quản lý trạng thái đóng/mở của nhiều section, bạn có thể:", "managingStepsList": {"stateObject": "<PERSON><PERSON><PERSON> trạng thái trong một state object", "functions": "T<PERSON><PERSON> các hàm để mở/đóng tất cả các section", "callback": "<PERSON><PERSON> dụng callback onExpand<PERSON><PERSON><PERSON> để cập nh<PERSON>t state"}, "gridIntegration": "<PERSON><PERSON><PERSON> với FormGrid", "gridIntegrationDesc": "FormSection có thể kết hợp với FormGrid để tạo layout phức tạp hơn cho form.", "advantages": "Ưu điểm", "advantagesList": {"gridLayout": "<PERSON><PERSON> chứ<PERSON> c<PERSON> field theo grid layout", "responsive": "Responsive v<PERSON>i các kích thư<PERSON>c màn hình khác nhau", "columns": "<PERSON><PERSON> dàng điều chỉnh số cột và khoảng cách"}, "gridUsage": "<PERSON><PERSON><PERSON> sử dụng", "gridUsageSteps": {"placeGrid": "Đặt FormGrid bên trong FormSection", "setColumns": "<PERSON><PERSON><PERSON><PERSON> lập số cột và kho<PERSON>ng cách", "spanColumns": "Sử dụng className=\"col-span-2\" để một field chiếm nhi<PERSON>u cột"}, "customStylingTitle": "Tùy chỉnh style", "customStylingDesc": "FormSection cung cấp nhiều props để tùy chỉnh style cho các phần khác nhau của section.", "stylingProps": "Props tùy chỉnh style", "stylingPropsList": {"className": "Tùy chỉnh style cho toàn bộ section", "titleClassName": "Tùy chỉnh style cho tiêu đề", "descriptionClassName": "Tùy chỉnh style cho mô tả", "contentClassName": "Tùy chỉnh style cho nội dung"}, "examples": "<PERSON><PERSON>", "examplesList": {"primaryBorder": "Thê<PERSON> viền màu primary: className=\"border-2 border-primary\"", "titleColor": "<PERSON><PERSON><PERSON> màu tiêu đề: titleClassName=\"text-primary\"", "italicDesc": "<PERSON><PERSON><PERSON> nghi<PERSON>ng mô tả: descriptionClassName=\"italic\"", "contentBg": "<PERSON><PERSON><PERSON> màu nền nội dung: contentClassName=\"bg-gray-50\""}, "variantsTitle": "<PERSON><PERSON><PERSON><PERSON> thể", "variantsDesc": "FormSection hỗ trợ nhiều biến thể khác nhau để phù hợp với thiết kế của ứng dụng.", "variantsList": {"default": "Biến thể mặc định với viền mỏng", "bordered": "<PERSON><PERSON><PERSON><PERSON> thể với viền dày hơn", "elevated": "<PERSON><PERSON><PERSON><PERSON> thể với hiệu <PERSON>ng shadow", "gradient": "<PERSON><PERSON><PERSON><PERSON> thể với nền gradient"}, "variantUsage": "<PERSON><PERSON><PERSON> sử dụng", "badgeAndIcon": "Badge và Icon Position", "badgeDesc": "FormSection hỗ trợ hiển thị badge bên cạnh tiêu đề để thể hiện trạng thái hoặc thông tin bổ sung.", "badgeFeatures": {"stringNumber": "Badge có thể là string, number hoặc ReactNode", "defaultBadge": "String và number sẽ được hiển thị trong Badge component mặc định", "customBadge": "ReactNode cho phép tùy chỉnh hoàn toàn badge"}, "iconPositionDesc": "FormSection cho phép tùy chỉnh vị trí của icon đóng/mở.", "iconPositions": {"right": "<PERSON><PERSON><PERSON> (mặc định)", "left": "<PERSON><PERSON><PERSON> t<PERSON>"}, "expandAll": "Mở tất cả", "collapseAll": "<PERSON><PERSON><PERSON> tất cả", "newsletterSubscription": "<PERSON><PERSON><PERSON> ký nhận bản tin", "managePreferences": "<PERSON><PERSON><PERSON><PERSON> lý tùy chọn nhận bản tin của bạn", "sizesDesc": "FormSection hỗ trợ nhiều kích thước để phù hợp với thiết kế của ứng dụng.", "sizesTitle": "<PERSON><PERSON><PERSON>", "sizesList": {"small": "<PERSON><PERSON><PERSON> thước nhỏ, phù hợp cho form gọn nhẹ", "medium": "<PERSON><PERSON><PERSON> thư<PERSON><PERSON> trung bình (mặc định)", "large": "<PERSON><PERSON><PERSON> thư<PERSON> lớn, ph<PERSON> hợp cho form cần nhấn mạnh"}, "sizeUsage": "Sử dụng prop size đ<PERSON> chọn kích thước", "animationDesc": "FormSection hỗ trợ nhiều hiệu ứng animation khi đóng/mở section.", "animationTypes": "Loại Animation", "animationTypesList": {"fade": "<PERSON><PERSON><PERSON> ứng mờ dần khi đóng/mở", "slide": "<PERSON><PERSON><PERSON> <PERSON>ng tr<PERSON><PERSON><PERSON> lên/xuống khi đóng/mở", "both": "<PERSON><PERSON><PERSON> hợp cả hiệu ứng mờ dần và trư<PERSON>t"}, "animationUsage": "Sử dụng các prop animated và animationType", "accordionDesc": "FormSection hỗ trợ chế độ accordion, cho phép chỉ mở một section tại một thời điểm.", "howItWorks": "<PERSON><PERSON><PERSON> ho<PERSON> động", "accordionWorkingList": {"oneAtTime": "Các section trong cùng một nhóm accordion sẽ đóng lại khi một section khác được mở", "uniqueId": "Mỗi section cần có một ID duy nhất", "sameAccordionId": "Các section trong cùng một nhóm cần có cùng accordionId"}, "accordionUsage": "Sử dụng các prop accordionId và id"}, "analytics": {"title": "Analytics Components", "description": "Các components hiển thị dữ liệu phân tích và báo cáo.", "metricDashboard": {"title": "Metric Dashboard", "description": "Dashboard hiển thị các chỉ số KPI chính với khả năng tùy chỉnh."}, "comparisonChart": {"title": "Comparison Chart", "description": "<PERSON><PERSON><PERSON><PERSON> đồ so sánh dữ liệu theo nhiều chiều với nhiều loại biểu đồ khác nhau."}, "chartPlaceholder": "<PERSON>iểu đồ sẽ được hiển thị ở đây", "sparkline": "B<PERSON>ể<PERSON> đồ mini", "previousPeriod": "Kỳ trước", "export": "<PERSON><PERSON><PERSON> b<PERSON>o c<PERSON>o", "customize": "<PERSON><PERSON><PERSON> chỉnh"}, "chat": {"title": "Chat Components", "description": "Các components hỗ trợ tính năng chat và tương tác với người dùng.", "chatInterface": {"title": "Chat Interface", "description": "Giao diện chat với khách hàng hỗ trợ nhiều loại tin nhắn và tương tác."}, "loadingMessages": "<PERSON><PERSON> tải tin nhắn...", "loadMore": "<PERSON><PERSON><PERSON> thêm tin <PERSON>n", "typeMessage": "<PERSON><PERSON><PERSON><PERSON> tin nhắn..."}, "marketing": {"title": "Marketing Components", "description": "Các components hỗ trợ tính năng marketing và quản lý chiến dịch.", "campaignBuilder": {"title": "Campaign Builder", "description": "Giao diện xây dựng và quản lý chiến dịch marketing."}, "contentTracker": {"title": "Content Performance Tracker", "description": "<PERSON> hiệu su<PERSON>t của nội dung marketing."}, "audienceBuilder": {"title": "Audience Segment Builder", "description": "<PERSON><PERSON><PERSON> dựng và quản lý phân khúc khách hàng."}, "emailEditor": {"title": "Email Template Editor", "description": "<PERSON>r<PERSON><PERSON> soạn thảo mẫu email marketing."}}, "ai": {"title": "AI Components", "description": "Các components hỗ trợ tính năng AI và quản lý mô hình.", "modelMonitor": {"title": "Model Performance Monitor", "description": "<PERSON> hiệu su<PERSON>t của mô hình AI."}, "promptEditor": {"title": "Prompt Template Editor", "description": "<PERSON>r<PERSON><PERSON> soạn thảo mẫu prompt cho mô hình ngôn ngữ."}, "datasetManager": {"title": "Dataset Manager", "description": "<PERSON><PERSON><PERSON><PERSON> lý bộ dữ liệu huấn luyện và kiểm thử."}, "explainabilityViewer": {"title": "AI Explainability Viewer", "description": "<PERSON><PERSON><PERSON> thị gi<PERSON>i thích cho các quyết đ<PERSON><PERSON> của AI."}}, "comingSoon": "Component đ<PERSON> đ<PERSON><PERSON><PERSON> ph<PERSON>t triển"}}