import React from 'react';
import useChatNotification from '@/shared/hooks/common/useChatNotification';
import { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useWebappChatWebSocket } from '@/shared/hooks/chat/useWebappChatWebSocket';
import { WebappChatConfig } from '@/shared/services/webapp-chat-websocket.service';
import { useAuth } from '@/shared/hooks/auth/useAuth';
import ChatContent from './ChatContent';
import ChatHeader from './ChatHeader';
import ChatInput from './ChatInput';

// Message type
interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  avatar?: string;
}

interface ChatPanelProps {
  onClose: () => void;
  onKeywordDetected?: (keyword: string) => void;
  // WebSocket configuration (optional)
  websocketConfig?: WebappChatConfig;
  conversationId?: number;
  // Mode: 'websocket' for real chat, 'demo' for simulation
  mode?: 'websocket' | 'demo';
}

const ChatPanel = ({
  onClose,
  onKeywordDetected,
  websocketConfig,
  conversationId,
  mode = 'websocket' // Default to WebSocket mode
}: ChatPanelProps) => {
  const { t } = useTranslation(['common', 'chat']);
  const { getToken } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { notifications, addNotification, removeNotification } = useChatNotification();

  // Default WebSocket config
  const defaultWebSocketConfig: WebappChatConfig = useMemo(() => ({
    url: import.meta.env.VITE_WEBSOCKET_URL || 'ws://localhost:3001',
    namespace: 'webapp-chat',
    auth: {
      token: getToken() || undefined,
    },
    autoConnect: true,
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
    timeout: 20000,
  }), [getToken]);

  // Use WebSocket only if mode is 'websocket'
  const shouldUseWebSocket = mode === 'websocket';

  // WebSocket chat hook (only when enabled)
  const {
    isConnected,
    connectionStatus,
    connect,
    currentConversationId,
    messages: wsMessages,
    sendMessage: wsSendMessage,
    clearMessages: wsClearMessages,
    streamingMessage,
    isStreaming,
    isAITyping,
    startTyping,
    stopTyping,
    lastError,
    clearError,
  } = useWebappChatWebSocket(shouldUseWebSocket ? {
    config: websocketConfig || defaultWebSocketConfig,
    conversationId,
    autoJoinConversation: true,
    enableTypingIndicator: true,
  } : {});

  // Convert WebSocket messages to ChatContent format
  const allMessages: Message[] = React.useMemo(() => {
    if (!shouldUseWebSocket) {
      return messages; // Use local state for demo mode
    }

    const convertedMessages = wsMessages.map(msg => ({
      id: msg.id,
      content: msg.content,
      sender: msg.sender,
      timestamp: new Date(msg.timestamp),
      avatar: msg.sender === 'ai' ? '/assets/images/ai-agents/assistant-robot.svg' : undefined,
    }));

    // Add streaming message if exists
    if (streamingMessage) {
      convertedMessages.push({
        id: streamingMessage.id,
        content: streamingMessage.content,
        sender: streamingMessage.sender,
        timestamp: new Date(streamingMessage.timestamp),
        avatar: '/assets/images/ai-agents/assistant-robot.svg',
      });
    }

    return convertedMessages;
  }, [shouldUseWebSocket, messages, wsMessages, streamingMessage]);

  // Handle connection errors (silent - không hiển thị notification)
  useEffect(() => {
    if (shouldUseWebSocket && lastError) {
      console.error('[ChatPanel] WebSocket error:', lastError);
      clearError();
    }
  }, [shouldUseWebSocket, lastError, clearError]);

  // Handle new chat
  const handleNewChat = () => {
    if (shouldUseWebSocket) {
      wsClearMessages();
      clearError();
    } else {
      setMessages([]);
    }
  };

  // Handle send message
  const handleSendMessage = async (content: string) => {
    if (shouldUseWebSocket) {
      // WebSocket mode - real chat
      try {
        setIsLoading(true);
        console.log('[ChatPanel] Sending message:', content);
        await wsSendMessage(content);
        console.log('[ChatPanel] Message sent successfully');
      } catch (error) {
        console.error('[ChatPanel] Failed to send message:', error);
        showNotification('error', t('chat:sendMessageError'));
        // Clear any error state
        clearError();
      } finally {
        setIsLoading(false);
        console.log('[ChatPanel] Loading state cleared');
      }
    } else {
      // Demo mode - simulate chat
      const userMessage: Message = {
        id: Date.now().toString(),
        content,
        sender: 'user',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, userMessage]);
      setIsLoading(true);

      // Simulate AI response after a delay
      setTimeout(() => {
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: `This is a response to: "${content}"`,
          sender: 'ai',
          timestamp: new Date(),
          avatar: '/assets/images/ai-agents/assistant-robot.svg',
        };

        setMessages(prev => [...prev, aiMessage]);
        setIsLoading(false);
      }, 1000);
    }
  };



  // Custom notification handler
  const showNotification = (type: 'success' | 'error' | 'warning' | 'info', message: string) => {
    console.log(`[ChatPanel] Showing notification: ${type} - ${message}`);

    // Thêm notification mới và tự động xóa sau 5 giây
    const id = addNotification(type, message, 5000);
    console.log(`[ChatPanel] Added notification with ID: ${id}`);

    // Kiểm tra xem notification đã được thêm vào state chưa
    console.log(`[ChatPanel] Current notifications:`, notifications);

    // Đảm bảo ChatContent được cập nhật
    setTimeout(() => {
      console.log(
        `[ChatPanel] Checking if notification ${id} is visible:`,
        notifications.some((n: unknown) => (n as unknown as { id: string }).id === id)
      );
    }, 500);

    return id;
  };



  return (
    <div className="flex flex-col h-full bg-white dark:bg-dark relative w-full">
      {/* Header cố định ở trên cùng với z-index cao */}
      <div className="sticky top-0 z-10 bg-white dark:bg-dark shadow-sm mb-4 flex-shrink-0">
        <ChatHeader onNewChat={handleNewChat} onClose={onClose} />
      </div>

      {/* Messages area - scrollable */}
      <div className="flex-1 overflow-hidden">
        <ChatContent
          messages={allMessages}
          isLoading={isLoading || (shouldUseWebSocket && (isAITyping || isStreaming))}
          notifications={notifications}
          onRemoveNotification={removeNotification}
        />
      </div>

      {/* Input area - fixed at bottom */}
      <div className="flex-shrink-0">
        <ChatInput
          onSendMessage={handleSendMessage}
          onKeywordDetected={onKeywordDetected}
          showNotification={showNotification}
        />
      </div>
    </div>
  );
};

export default ChatPanel;
