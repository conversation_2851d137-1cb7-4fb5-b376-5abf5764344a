/**
 * Webapp Chat WebSocket Service
 * Chuyên dụng cho webapp chat với AI agent
 */

import { io, Socket } from 'socket.io-client';

// Backend events từ webapp-chat.gateway.ts
export enum WebappChatEvents {
  // Sự kiện tin nhắn
  SEND_MESSAGE = 'webapp_chat:send_message',
  MESSAGE_RECEIVED = 'webapp_chat:message_received',
  AI_RESPONSE = 'webapp_chat:ai_response',

  // Sự kiện cuộc hội thoại
  JOIN_CONVERSATION = 'webapp_chat:join_conversation',
  LEAVE_CONVERSATION = 'webapp_chat:leave_conversation',
  CONVERSATION_JOINED = 'webapp_chat:conversation_joined',
  CONVERSATION_LEFT = 'webapp_chat:conversation_left',

  // Sự kiện trạng thái
  TYPING_START = 'webapp_chat:typing_start',
  TYPING_STOP = 'webapp_chat:typing_stop',
  USER_TYPING = 'webapp_chat:user_typing',
  AI_TYPING = 'webapp_chat:ai_typing',

  // Sự kiện streaming
  AI_STREAM_CHUNK = 'webapp_chat:ai_stream_chunk',
  AI_STREAM_END = 'webapp_chat:ai_stream_end',

  // Sự kiện lỗi và hệ thống
  ERROR = 'webapp_chat:error',
  CONNECTION_STATUS = 'webapp_chat:connection_status',
  AGENT_STATUS = 'webapp_chat:agent_status',
}

// Types cho webapp chat
export interface WebappChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: string;
  conversationId: number;
  messageId?: number;
  isStreaming?: boolean;
  streamComplete?: boolean;
}

export interface WebappChatConfig {
  url: string;
  namespace?: string;
  auth?: {
    token?: string;
    userId?: string;
  };
  autoConnect?: boolean;
  reconnection?: boolean;
  reconnectionAttempts?: number;
  reconnectionDelay?: number;
  timeout?: number;
}

export interface ConversationInfo {
  conversationId: number;
  status: string;
  language: string;
  timestamp: number;
}

export interface TypingStatus {
  conversationId: number;
  isTyping: boolean;
  userId?: string;
  username?: string;
  timestamp: number;
}

export interface StreamChunk {
  messageId: number;
  conversationId: number;
  chunk: string;
  timestamp: number;
}

export interface ConnectionStatusInfo {
  status: 'connected' | 'disconnected' | 'connecting' | 'error';
  message?: string;
  timestamp: number;
}

export type ConnectionStatus = 'connected' | 'disconnected' | 'connecting' | 'error';

// Event listener type
export type EventListener = (...args: any[]) => void;

/**
 * Webapp Chat WebSocket Service Class
 */
export class WebappChatWebSocketService {
  private socket: Socket | null = null;
  private config: WebappChatConfig;
  private connectionStatus: ConnectionStatus = 'disconnected';
  private eventListeners: Map<string, Set<EventListener>> = new Map();
  private currentConversationId: number | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  constructor(config: WebappChatConfig) {
    this.config = {
      namespace: 'webapp-chat',
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      timeout: 20000,
      ...config,
    };

    if (this.config.autoConnect) {
      this.connect();
    }
  }

  /**
   * Connect to WebSocket server
   */
  public connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve();
        return;
      }

      this.setConnectionStatus('connecting');

      // Tạo URL với namespace
      const socketUrl = this.config.namespace
        ? `${this.config.url}/${this.config.namespace}`
        : this.config.url;

      this.socket = io(socketUrl, {
        autoConnect: true,
        reconnection: this.config.reconnection,
        reconnectionAttempts: this.config.reconnectionAttempts,
        reconnectionDelay: this.config.reconnectionDelay,
        timeout: this.config.timeout,
        auth: this.config.auth,
      });

      // Connection events
      this.socket.on('connect', () => {
        console.log('[WebappChatWebSocket] Connected to server');
        this.setConnectionStatus('connected');
        this.reconnectAttempts = 0;
        resolve();
      });

      this.socket.on('disconnect', reason => {
        console.log(`[WebappChatWebSocket] Disconnected: ${reason}`);
        this.setConnectionStatus('disconnected');
        this.currentConversationId = null;
      });

      this.socket.on('connect_error', error => {
        console.error('[WebappChatWebSocket] Connection error:', error);
        this.setConnectionStatus('error');
        this.reconnectAttempts++;

        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          reject(new Error(`Failed to connect after ${this.maxReconnectAttempts} attempts`));
        }
      });

      // Setup event listeners
      this.setupEventListeners();
    });
  }

  /**
   * Disconnect from server
   */
  public disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.setConnectionStatus('disconnected');
    this.currentConversationId = null;
  }

  /**
   * Check if connected
   */
  public isConnected(): boolean {
    return this.socket?.connected || false;
  }

  /**
   * Get connection status
   */
  public getConnectionStatus(): ConnectionStatus {
    return this.connectionStatus;
  }

  /**
   * Get current conversation ID
   */
  public getCurrentConversationId(): number | null {
    return this.currentConversationId;
  }

  /**
   * Set connection status and emit event
   */
  private setConnectionStatus(status: ConnectionStatus): void {
    this.connectionStatus = status;
    this.emit('connection_status_changed', { status, timestamp: Date.now() });
  }

  /**
   * Add event listener
   */
  public on(event: string, listener: EventListener): () => void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }

    this.eventListeners.get(event)!.add(listener);

    // Return unsubscribe function
    return () => {
      const listeners = this.eventListeners.get(event);
      if (listeners) {
        listeners.delete(listener);
        if (listeners.size === 0) {
          this.eventListeners.delete(event);
        }
      }
    };
  }

  /**
   * Emit event to listeners
   */
  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error(`[WebappChatWebSocket] Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Setup WebSocket event listeners
   */
  private setupEventListeners(): void {
    if (!this.socket) return;

    // Connection status events
    this.socket.on(WebappChatEvents.CONNECTION_STATUS, (data: ConnectionStatusInfo) => {
      this.emit('connection_status', data);
    });

    // Conversation events
    this.socket.on(WebappChatEvents.CONVERSATION_JOINED, (data: ConversationInfo) => {
      this.currentConversationId = data.conversationId;
      this.emit('conversation_joined', data);
    });

    this.socket.on(WebappChatEvents.CONVERSATION_LEFT, (data: ConversationInfo) => {
      if (this.currentConversationId === data.conversationId) {
        this.currentConversationId = null;
      }
      this.emit('conversation_left', data);
    });

    // Message events
    this.socket.on(WebappChatEvents.MESSAGE_RECEIVED, (data: any) => {
      this.emit('message_received', data);
    });

    this.socket.on(WebappChatEvents.AI_RESPONSE, (data: WebappChatMessage) => {
      this.emit('ai_response', data);
    });

    // Streaming events
    this.socket.on(WebappChatEvents.AI_STREAM_CHUNK, (data: StreamChunk) => {
      this.emit('ai_stream_chunk', data);
    });

    this.socket.on(WebappChatEvents.AI_STREAM_END, (data: any) => {
      this.emit('ai_stream_end', data);
    });

    // Typing events
    this.socket.on(WebappChatEvents.AI_TYPING, (data: TypingStatus) => {
      this.emit('ai_typing', data);
    });

    this.socket.on(WebappChatEvents.USER_TYPING, (data: TypingStatus) => {
      this.emit('user_typing', data);
    });

    // Error events
    this.socket.on(WebappChatEvents.ERROR, (data: any) => {
      this.emit('error', data);
    });
  }

  /**
   * Join conversation
   */
  public async joinConversation(conversationId?: number): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('Not connected to server');
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Join conversation timeout'));
      }, 10000); // 10 second timeout

      this.socket!.emit(WebappChatEvents.JOIN_CONVERSATION, { conversationId }, (response: any) => {
        clearTimeout(timeout);

        if (response && response.event === WebappChatEvents.CONVERSATION_JOINED) {
          resolve();
        } else {
          reject(new Error(response?.data?.message || 'Failed to join conversation'));
        }
      });
    });
  }

  /**
   * Leave conversation
   */
  public async leaveConversation(conversationId: number): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('Not connected to server');
    }

    return new Promise((resolve, reject) => {
      this.socket!.emit(
        WebappChatEvents.LEAVE_CONVERSATION,
        { conversationId },
        (response: any) => {
          if (response.event === WebappChatEvents.CONVERSATION_LEFT) {
            resolve();
          } else {
            reject(new Error('Failed to leave conversation'));
          }
        }
      );
    });
  }

  /**
   * Send message
   */
  public async sendMessage(content: string, conversationId?: number): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('Not connected to server');
    }

    let targetConversationId = conversationId || this.currentConversationId;

    // Nếu chưa có conversation, tự động join conversation mới
    if (!targetConversationId) {
      try {
        await this.joinConversation();
        targetConversationId = this.currentConversationId;

        if (!targetConversationId) {
          throw new Error('Failed to create conversation');
        }
      } catch (error) {
        throw new Error('Failed to join conversation');
      }
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Message send timeout'));
      }, 10000); // 10 second timeout

      this.socket!.emit(
        WebappChatEvents.SEND_MESSAGE,
        {
          content,
          conversationId: targetConversationId,
        },
        (response: any) => {
          clearTimeout(timeout);

          if (
            response &&
            response.event === WebappChatEvents.MESSAGE_RECEIVED &&
            response.data &&
            response.data.status === 'success'
          ) {
            resolve();
          } else {
            reject(new Error(response?.data?.message || 'Failed to send message'));
          }
        }
      );
    });
  }

  /**
   * Start typing indicator
   */
  public startTyping(conversationId?: number): void {
    if (!this.isConnected()) return;

    const targetConversationId = conversationId || this.currentConversationId;
    if (!targetConversationId) return;

    this.socket!.emit(WebappChatEvents.TYPING_START, {
      conversationId: targetConversationId,
    });
  }

  /**
   * Stop typing indicator
   */
  public stopTyping(conversationId?: number): void {
    if (!this.isConnected()) return;

    const targetConversationId = conversationId || this.currentConversationId;
    if (!targetConversationId) return;

    this.socket!.emit(WebappChatEvents.TYPING_STOP, {
      conversationId: targetConversationId,
    });
  }

  /**
   * Destroy the service
   */
  public destroy(): void {
    this.disconnect();
    this.eventListeners.clear();
  }
}

// Singleton instance
let webappChatWebSocketService: WebappChatWebSocketService | null = null;

/**
 * Get or create WebappChatWebSocket service instance
 */
export function getWebappChatWebSocketService(
  config?: WebappChatConfig
): WebappChatWebSocketService {
  if (!webappChatWebSocketService && config) {
    webappChatWebSocketService = new WebappChatWebSocketService(config);
  }

  if (!webappChatWebSocketService) {
    throw new Error('WebappChatWebSocket service not initialized. Please provide config.');
  }

  return webappChatWebSocketService;
}

/**
 * Initialize WebappChatWebSocket service
 */
export function initializeWebappChatWebSocket(
  config: WebappChatConfig
): WebappChatWebSocketService {
  if (webappChatWebSocketService) {
    webappChatWebSocketService.destroy();
  }

  webappChatWebSocketService = new WebappChatWebSocketService(config);
  return webappChatWebSocketService;
}
