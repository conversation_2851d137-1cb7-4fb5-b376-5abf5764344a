import { useWebSocketContext } from '../contexts/WebSocketContext';

/**
 * Hook để sử dụng WebSocket
 * @returns WebSocket instance và các phương thức liên quan
 */
export function useWebSocket() {
  const { socket, isConnected, connect, disconnect } = useWebSocketContext();

  if (!socket && import.meta.env.DEV) {
    console.warn('useWebSocket: Socket is null. Make sure you are using WebSocketProvider.');
  }

  return {
    socket,
    isConnected,
    connect,
    disconnect,
  };
}

export default useWebSocket;
