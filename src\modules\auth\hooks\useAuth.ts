import { useCallback } from 'react';

import { useAppDispatch, useAppSelector, RootState } from '@/shared/store';
import {
  loginSuccess,
  logout as logoutAction,
  updateToken,
  updateUser,
  saveVerifyInfo as saveVerifyInfoAction,
  saveTwoFactorInfo as saveTwoFactorInfoAction,
} from '@/shared/store/slices/authSlice';

import { User } from '../types/company-auth.types';

/**
 * Hook quản lý thông tin xác thực
 * Tập trung việc lưu trữ và truy xuất token, thông tin người dùng
 */
export const useAuth = () => {
  const dispatch = useAppDispatch();
  const auth = useAppSelector((state: RootState) => state.auth);

  /**
   * L<PERSON>u thông tin đăng nhập
   */
  const setAuth = useCallback(
    (data: { accessToken: string; refreshToken?: string; expiresIn: number; user?: User }) => {
      // <PERSON><PERSON><PERSON> token vào Redux store
      dispatch(
        loginSuccess({
          accessToken: data.accessToken,
          refreshToken: data.refreshToken,
          expiresIn: data.expiresIn,
          user: data.user,
        })
      );

      // Đồng thời lưu token vào localStorage để đảm bảo
      localStorage.setItem('token', data.accessToken);
      console.log('Token saved to localStorage:', data.accessToken);

      // Trả về Promise để có thể await
      return Promise.resolve();
    },
    [dispatch]
  );

  /**
   * Cập nhật token
   */
  const updateAuthToken = useCallback(
    (data: { accessToken: string; refreshToken?: string; expiresIn: number }) => {
      dispatch(
        updateToken({
          accessToken: data.accessToken,
          refreshToken: data.refreshToken,
          expiresIn: data.expiresIn,
        })
      );

      // Cập nhật token trong localStorage
      localStorage.setItem('token', data.accessToken);
      console.log('Token updated in localStorage:', data.accessToken);
    },
    [dispatch]
  );

  /**
   * Cập nhật thông tin người dùng
   */
  const updateAuthUser = useCallback(
    (user: User) => {
      dispatch(updateUser(user));
    },
    [dispatch]
  );

  /**
   * Đăng xuất
   */
  const clearAuth = useCallback(() => {
    dispatch(logoutAction());
    // Xóa token khỏi localStorage khi đăng xuất
    localStorage.removeItem('token');
    console.log('Token removed from localStorage');
  }, [dispatch]);

  /**
   * Lưu thông tin xác thực email
   */
  const saveVerifyInfo = useCallback(
    (data: { verifyToken: string; expiresIn?: number; expiresAt?: number; info?: unknown[] }) => {
      dispatch(saveVerifyInfoAction(data));
    },
    [dispatch]
  );

  /**
   * Lưu thông tin xác thực 2FA
   */
  const saveTwoFactorInfo = useCallback(
    (data: {
      verifyToken: string;
      expiresAt: number;
      enabledMethods: Array<{ type: string; value: string }>;
    }) => {
      dispatch(saveTwoFactorInfoAction(data));
    },
    [dispatch]
  );

  /**
   * Kiểm tra xem người dùng đã đăng nhập chưa
   */
  const isAuthenticated = auth.isAuthenticated && !!auth.accessToken;

  /**
   * Lấy token
   */
  const getToken = useCallback(() => auth.accessToken, [auth.accessToken]);

  /**
   * Kiểm tra token có hợp lệ không
   * Token hợp lệ khi tồn tại và chưa hết hạn
   */
  const isTokenValid = useCallback(() => {
    if (!auth.accessToken) {return false;}

    // Nếu không có expiresIn, coi như token không hợp lệ
    if (!auth.expiresIn) {return false;}

    // Kiểm tra token đã hết hạn chưa
    const now = Date.now();
    const expiresAt = auth.expiresIn * 1000; // Chuyển đổi từ giây sang mili giây

    return now < expiresAt;
  }, [auth.accessToken, auth.expiresIn]);

  return {
    // State
    accessToken: auth.accessToken,
    refreshToken: auth.refreshToken,
    expiresIn: auth.expiresIn,
    user: auth.user,
    isAuthenticated,

    // Verification state
    verifyToken: auth.verifyToken,
    verifyExpiresIn: auth.verifyExpiresIn,
    verifyExpiresAt: auth.verifyExpiresAt,
    verifyInfo: auth.verifyInfo,

    // Two-factor auth state
    twoFactorVerifyToken: auth.twoFactorVerifyToken,
    twoFactorExpiresIn: auth.twoFactorExpiresIn,
    twoFactorExpiresAt: auth.twoFactorExpiresAt,
    enabledMethods: auth.enabledMethods,

    // Actions
    setAuth,
    updateAuthToken,
    updateAuthUser,
    clearAuth,
    saveVerifyInfo,
    saveTwoFactorInfo,
    getToken,
    isTokenValid,
  };
};
